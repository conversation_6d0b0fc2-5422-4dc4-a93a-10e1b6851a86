#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查端点测试脚本
"""

import requests
import json
import sys

def test_health_endpoint():
    """测试健康检查端点"""
    url = "http://127.0.0.1:8001/health"
    
    try:
        print(f"🔍 正在测试健康端点: {url}")
        response = requests.get(url, timeout=5)
        
        print(f"✅ 状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"📊 JSON数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get("status") == "healthy":
                    print("🎉 健康检查端点正常工作！")
                    return True
                else:
                    print("⚠️  健康检查端点返回了意外的状态")
                    return False
            except json.JSONDecodeError:
                print("⚠️  响应不是有效的JSON格式")
                return False
        else:
            print(f"❌ 健康检查失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 服务可能未启动")
        print("💡 请确保应用程序正在运行在端口 8001")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_port_status():
    """检查端口状态"""
    import socket
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('127.0.0.1', 8001))
            if result == 0:
                print("✅ 端口 8001 正在监听")
                return True
            else:
                print("❌ 端口 8001 未监听")
                return False
    except Exception as e:
        print(f"❌ 检查端口状态失败: {e}")
        return False

if __name__ == "__main__":
    print("🏥 健康检查端点测试")
    print("=" * 50)
    
    # 首先检查端口状态
    port_ok = check_port_status()
    
    if port_ok:
        # 端口正常，测试健康端点
        health_ok = test_health_endpoint()
        
        if health_ok:
            print("\n🎯 结论: 健康检查端点已启用且正常工作")
            sys.exit(0)
        else:
            print("\n⚠️  结论: 端口开放但健康检查端点有问题")
            sys.exit(1)
    else:
        print("\n❌ 结论: 应用程序未运行或端口未开放")
        print("💡 建议: 启动应用程序后再次测试")
        sys.exit(1)
