#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 /v1/completions 端点
"""

import requests
import json
import sys

def test_completions_endpoint():
    """测试 completions 端点"""
    url = "http://127.0.0.1:8001/v1/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-123456"
    }
    
    # 测试1: 不指定模型（应该使用默认的 gemini-2.5-flash）
    payload1 = {
        "prompt": "Hello, how are you?",
        "max_tokens": 50
    }
    
    # 测试2: 指定模型
    payload2 = {
        "prompt": "What is the capital of France?",
        "model": "gemini-2.5-flash",
        "max_tokens": 50
    }
    
    # 测试3: 流式响应
    payload3 = {
        "prompt": "Tell me a short story",
        "model": "gemini-2.5-flash",
        "max_tokens": 100,
        "stream": True
    }
    
    test_cases = [
        ("不指定模型（默认模型测试）", payload1, False),
        ("指定模型测试", payload2, False),
        ("流式响应测试", payload3, True)
    ]
    
    for test_name, payload, is_stream in test_cases:
        print(f"\n🧪 {test_name}")
        print(f"📤 请求: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        print("-" * 50)
        
        try:
            if is_stream:
                # 流式请求
                response = requests.post(url, headers=headers, json=payload, stream=True, timeout=10)
                print(f"✅ 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("📡 流式响应:")
                    for line in response.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            if decoded_line.startswith('data: '):
                                print(f"  {decoded_line}")
                            if '[DONE]' in decoded_line:
                                break
                else:
                    print(f"❌ 错误响应: {response.text}")
            else:
                # 普通请求
                response = requests.post(url, headers=headers, json=payload, timeout=10)
                print(f"✅ 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"📄 响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        
                        # 检查响应格式
                        if "choices" in data and len(data["choices"]) > 0:
                            choice = data["choices"][0]
                            if "text" in choice:
                                print(f"💬 生成文本: {choice['text']}")
                            elif "message" in choice:
                                print(f"💬 生成消息: {choice['message'].get('content', '')}")
                        
                        # 检查使用的模型
                        if "model" in data:
                            print(f"🤖 使用模型: {data['model']}")
                            
                    except json.JSONDecodeError:
                        print(f"⚠️  响应不是有效的JSON: {response.text}")
                else:
                    print(f"❌ 错误响应: {response.text}")
                    
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败 - 服务可能未启动")
            return False
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    return True

def test_endpoint_availability():
    """测试端点可用性"""
    url = "http://127.0.0.1:8001/v1/completions"
    
    try:
        # 发送 OPTIONS 请求检查端点是否存在
        response = requests.options(url, timeout=5)
        print(f"🔍 OPTIONS 请求状态码: {response.status_code}")
        
        if response.status_code == 405:
            print("✅ 端点存在但不支持 OPTIONS 方法（正常）")
            return True
        elif response.status_code == 404:
            print("❌ 端点不存在")
            return False
        else:
            print(f"✅ 端点响应: {response.status_code}")
            return True
            
    except Exception as e:
        print(f"❌ 端点检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 /v1/completions 端点测试")
    print("=" * 60)
    
    # 首先检查端点可用性
    if test_endpoint_availability():
        print("\n🚀 开始功能测试...")
        success = test_completions_endpoint()
        
        if success:
            print("\n🎉 所有测试完成")
        else:
            print("\n⚠️  部分测试失败")
            sys.exit(1)
    else:
        print("\n❌ 端点不可用，请检查服务状态")
        sys.exit(1)
